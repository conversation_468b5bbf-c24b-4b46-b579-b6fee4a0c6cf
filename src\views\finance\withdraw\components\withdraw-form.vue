<template>
    <popup
        ref="popupRef"
        class="mr-2 inline"
        :clickModalClose="false"
        title="申请提现"
        :center="true"
        :async="true"
        @confirm="onSubmit"
        @close="handleClose"
        width="500px"
    >
        <!-- Trigger Start -->
        <template #trigger>
            <el-button type="primary" @click="handleButtonClick">申请提现</el-button>
        </template>
        <!-- Trigger End -->

        <el-form
            ref="formRef"
            :model="formData"
            :rules="rules"
            label-width="100px"
            class="demo-formData"
        >
            <div class="mb-4 p-3 bg-blue-50 rounded">
                <div class="text-sm text-gray-600 mb-2">提现配置信息：</div>
                <div class="text-sm">
                    <span class="text-gray-500">可提现金额：</span>
                    <span class="text-green-600 font-medium">¥{{ config.commission || '0.00' }}</span>
                </div>
                <div class="text-sm mt-1">
                    <span class="text-gray-500">提现范围：</span>
                    <span class="text-blue-600">¥{{ config.min_money || '0' }} ~ ¥{{ config.max_money || '0' }}</span>
                </div>
            </div>

            <el-form-item label="提现金额" prop="money">
                <el-input 
                    class="ls-input" 
                    v-model="formData.money" 
                    type="number"
                    placeholder="请输入提现金额"
                    :min="config.min_money"
                    :max="Math.min(config.max_money, config.commission)"
                >
                    <template #append>元</template>
                </el-input>
            </el-form-item>

            <el-form-item label="开户行" prop="opening_bank">
                <el-input
                    class="ls-input"
                    v-model="formData.opening_bank"
                    placeholder="请输入开户银行名称"
                    maxlength="50"
                />
            </el-form-item>

            <el-form-item label="开户人姓名" prop="real_name">
                <el-input
                    class="ls-input"
                    v-model="formData.real_name"
                    placeholder="请输入开户人姓名"
                    maxlength="20"
                />
            </el-form-item>

            <el-form-item label="银行账号" prop="bank">
                <el-input
                    class="ls-input"
                    v-model="formData.bank"
                    placeholder="请输入银行卡号"
                    maxlength="30"
                />
            </el-form-item>
        </el-form>
    </popup>
</template>

<script lang="ts" setup name="withdrawForm">
import Popup from '@/components/popup/index.vue'
import { apiWithdrawApply, apiWithdrawConfig } from '@/api/finance/withdraw'
import { reactive, ref } from 'vue'
import type { ElForm } from 'element-plus'
import feedback from '@/utils/feedback'

interface FormDataObj {
    money: string | number
    opening_bank: string
    real_name: string
    bank: string
}

type FormInstance = InstanceType<typeof ElForm>
const formRef = ref<FormInstance>()
const popupRef = ref<any>(null)

const emit = defineEmits(['refresh'])

const formData = ref<FormDataObj>({
    money: '',
    opening_bank: '',
    real_name: '',
    bank: ''
})

// 提现配置
const config = ref({
    min_money: 0,
    max_money: 0,
    commission: 0
})



// 表单校验规则
const rules = reactive({
    money: [
        { required: true, message: '请输入提现金额', trigger: 'blur' },
        {
            validator: (_rule: any, value: string | number, callback: any) => {
                const amount = Number(value)
                if (!amount || amount <= 0) {
                    callback(new Error('提现金额必须大于0'))
                    return
                }
                if (amount < config.value.min_money) {
                    callback(new Error(`提现金额不能少于${config.value.min_money}元`))
                    return
                }
                if (amount > config.value.max_money) {
                    callback(new Error(`提现金额不能超过${config.value.max_money}元`))
                    return
                }
                if (amount > config.value.commission) {
                    callback(new Error('提现金额不能超过可提现金额'))
                    return
                }
                callback()
            },
            trigger: 'blur'
        }
    ],
    opening_bank: [
        { required: true, message: '请输入开户银行', trigger: 'blur' }
    ],
    real_name: [
        { required: true, message: '请输入开户人姓名', trigger: 'blur' },
        { min: 2, max: 20, message: '开户人姓名长度在2到20个字符', trigger: 'blur' },
        {
            validator: (_rule: any, value: string, callback: any) => {
                if (value && !/^[\u4e00-\u9fa5a-zA-Z\s]+$/.test(value)) {
                    callback(new Error('开户人姓名只能包含中文、英文和空格'))
                    return
                }
                callback()
            },
            trigger: 'blur'
        }
    ],
    bank: [
        { required: true, message: '请输入银行卡号', trigger: 'blur' },
        {
            validator: (_rule: any, value: string, callback: any) => {
                if (value && !/^\d{16,19}$/.test(value)) {
                    callback(new Error('请输入正确的银行卡号'))
                    return
                }
                callback()
            },
            trigger: 'blur'
        }
    ]
})

// 弹窗关闭
const handleClose = (): void => {
    formRef.value?.resetFields()
    formData.value = {
        money: '',
        opening_bank: '',
        real_name: '',
        bank: ''
    }
}

// 按钮点击处理
const handleButtonClick = async (): Promise<void> => {
    try {
        const res = await apiWithdrawConfig()
        config.value = {
            min_money: res.min_money || 0,
            max_money: res.max_money || 0,
            commission: res.commission || 0
        }

        // 检查是否满足提现条件
        if (config.value.commission < config.value.min_money) {
            feedback.msgError(`可提现金额不足，最少需要${config.value.min_money}元才能申请提现`)
            return
        }

        // 条件满足，打开弹窗
        popupRef.value?.open()
    } catch (error: any) {
        feedback.msgError(error.message || '获取提现配置失败')
    }
}



// 提交表单
const onSubmit = (): void => {
    formRef.value?.validate(async (valid) => {
        if (!valid) return

        try {
            await apiWithdrawApply(formData.value)
            feedback.msgSuccess('申请提现成功')
            emit('refresh')
            popupRef.value?.close()
        } catch (error: any) {
            feedback.msgError(error.message || '申请提现失败')
        }
    })
}
</script>

<style scoped>
.bg-blue-50 {
    background-color: #eff6ff;
}
.rounded {
    border-radius: 0.375rem;
}
.text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
}
.text-gray-600 {
    color: #4b5563;
}
.text-gray-500 {
    color: #6b7280;
}
.text-green-600 {
    color: #16a34a;
}
.text-blue-600 {
    color: #2563eb;
}
.font-medium {
    font-weight: 500;
}
.mb-2 {
    margin-bottom: 0.5rem;
}
.mb-4 {
    margin-bottom: 1rem;
}
.mt-1 {
    margin-top: 0.25rem;
}
.p-3 {
    padding: 0.75rem;
}
</style>
