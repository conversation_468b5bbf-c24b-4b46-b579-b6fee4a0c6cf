<template>
    <!-- 统计版块 -->
    <el-card shadow="never" class="!border-none" v-loading="statisticsLoading">
        <div class="grid grid-cols-5 gap-4">
            <div class="text-center">
                <div class="text-2xl font-bold text-blue-600">¥{{ formatAmount(statistics.today_amount) }}</div>
                <div class="text-gray-500 mt-1">今日收益金额</div>
            </div>
            <div class="text-center">
                <div class="text-2xl font-bold text-green-600">¥{{ formatAmount(statistics.total_amount) }}</div>
                <div class="text-gray-500 mt-1">收益总金额</div>
            </div>
            <div class="text-center">
                <div class="text-2xl font-bold text-orange-600">¥{{ formatAmount(statistics.can_withdraw_amount) }}</div>
                <div class="text-gray-500 mt-1">可提现金额</div>
            </div>
            <div class="text-center">
                <div class="text-2xl font-bold text-purple-600">¥{{ formatAmount(statistics.has_withdraw_amount) }}</div>
                <div class="text-gray-500 mt-1">已提现金额</div>
            </div>
            <div class="text-center">
                <div class="text-2xl font-bold text-red-600">¥{{ formatAmount(statistics.wait_withdraw_amount) }}</div>
                <div class="text-gray-500 mt-1">提现中金额</div>
            </div>
        </div>
    </el-card>

    <!-- 搜索表单 -->
    <el-card shadow="never" class="!border-none mt-4">
        <el-form :model="formData" inline>
            <el-form-item label="提现卡号">
                <el-input class="ls-input" v-model="formData.bank" placeholder="请输入银行卡号" />
            </el-form-item>
            <el-form-item label="提现状态">
                <el-select class="ls-input" v-model="formData.status" placeholder="请选择提现状态" style="width: 150px;">
                    <el-option label="全部" value="" />
                    <el-option label="待审核" value="1" />
                    <el-option label="提现中" value="2" />
                    <el-option label="提现成功" value="3" />
                    <el-option label="提现失败" value="4" />
                </el-select>
            </el-form-item>
            <el-form-item label="提现时间">
                <data-picker
                    class="ls-input"
                    style="width: 280px"
                    v-model:start_time="formData.start_time"
                    v-model:end_time="formData.end_time"
                />
            </el-form-item>
            <el-form-item>
                <div class="ml-4">
                    <el-button type="primary" @click="handleSearch">查询</el-button>
                    <el-button @click="handleReset">重置</el-button>
                </div>
            </el-form-item>
        </el-form>
    </el-card>

    <!-- 列表 -->
    <el-card shadow="never" class="mt-4 !border-none">
        <div class="" style="display: inline-block;">
            <withdraw-form @refresh="getListsWithStats" />
        </div>
        
        <div class="">
            <el-table
                ref="tableData"
                size="large"
                :data="pager.lists"
                v-loading="pager.loading"
                :empty-text="pager.lists.length === 0 && !pager.loading ? '暂无提现记录' : ''"
            >
                <el-table-column property="money" label="提现金额" min-width="120">
                    <template #default="scope">
                        <div class="text-red-600 font-medium">¥{{ formatAmount(scope.row.money) }}</div>
                    </template>
                </el-table-column>
                <el-table-column property="service_ratio" label="手续费" min-width="100">
                    <template #default="scope">
                        <div>¥{{ formatAmount(scope.row.service_ratio) }}</div>
                    </template>
                </el-table-column>
                <el-table-column property="left_money" label="应到账金额" min-width="120">
                    <template #default="scope">
                        <div class="text-green-600 font-medium">¥{{ formatAmount(scope.row.left_money) }}</div>
                    </template>
                </el-table-column>
                <el-table-column property="opening_bank" label="提现银行卡" min-width="150">
                    <template #default="scope">
                        <div>{{ scope.row.opening_bank || '-' }}</div>
                    </template>
                </el-table-column>
                <el-table-column property="real_name" label="开户人姓名" min-width="120">
                    <template #default="scope">
                        <div>{{ scope.row.real_name || '-' }}</div>
                    </template>
                </el-table-column>
                <el-table-column property="bank" label="银行卡号" min-width="180">
                    <template #default="scope">
                        <div>{{ scope.row.bank || '-' }}</div>
                    </template>
                </el-table-column>
                <el-table-column property="status_desc" label="提现状态" min-width="100">
                    <template #default="scope">
                        <el-tag
                            :type="getStatusType(scope.row.status)"
                            effect="plain"
                        >
                            {{ scope.row.status_desc }}
                        </el-tag>
                    </template>
                </el-table-column>
                <el-table-column property="create_time" label="提现时间" min-width="180">
                    <template #default="scope">
                        <div>{{ scope.row.create_time }}</div>
                    </template>
                </el-table-column>
                <el-table-column label="操作" min-width="100" fixed="right">
                    <template #default="scope">
                        <el-button type="primary" link @click="showDetail(scope.row)">详情</el-button>
                    </template>
                </el-table-column>
            </el-table>
        </div>

        <div class="flex justify-end mt-4">
            <pagination v-model="pager" @change="getListsWithStats" />
        </div>
    </el-card>

    <!-- 详情弹窗 -->
    <el-dialog
        v-model="detailVisible"
        title="提现详情"
        width="600px"
        :close-on-click-modal="false"
    >
        <div v-if="currentDetail" class="detail-content">
            <el-descriptions :column="2" border>
                <el-descriptions-item label="提现金额">
                    <span class="text-red-600 font-medium">¥{{ formatAmount(currentDetail.money) }}</span>
                </el-descriptions-item>
                <el-descriptions-item label="手续费">
                    ¥{{ formatAmount(currentDetail.service_ratio) }}
                </el-descriptions-item>
                <el-descriptions-item label="应到账金额">
                    <span class="text-green-600 font-medium">¥{{ formatAmount(currentDetail.left_money) }}</span>
                </el-descriptions-item>
                <el-descriptions-item label="提现状态">
                    <el-tag :type="getStatusType(currentDetail.status)" effect="plain">
                        {{ currentDetail.status_desc }}
                    </el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="开户行">
                    {{ currentDetail.opening_bank || '-' }}
                </el-descriptions-item>
                <el-descriptions-item label="开户人姓名">
                    {{ currentDetail.real_name || '-' }}
                </el-descriptions-item>
                <el-descriptions-item label="银行卡号">
                    {{ currentDetail.bank || '-' }}
                </el-descriptions-item>
                <el-descriptions-item label="申请时间">
                    {{ currentDetail.create_time }}
                </el-descriptions-item>
                <el-descriptions-item label="更新时间">
                    {{ currentDetail.update_time || '-' }}
                </el-descriptions-item>

                <!-- 提现成功时显示的字段 -->
                <template v-if="currentDetail.status === 3">
                    <el-descriptions-item label="转账凭证" :span="2">
                        <el-image
                            v-if="currentDetail.transfer_voucher"
                            style="width: 100px; height: 100px"
                            :src="currentDetail.transfer_voucher"
                            :preview-src-list="[currentDetail.transfer_voucher]"
                            :hide-on-click-modal="true"
                            :preview-teleported="true"
                            fit="cover"
                        />
                        <span v-else>-</span>
                    </el-descriptions-item>
                    <el-descriptions-item label="转账时间">
                        {{ currentDetail.transfer_time }}
                    </el-descriptions-item>
                    <el-descriptions-item label="转账备注">
                        {{ currentDetail.transfer_remark || '-' }}
                    </el-descriptions-item>
                </template>

                <!-- 提现失败时显示的字段 -->
                <template v-if="currentDetail.status === 4">
                    <el-descriptions-item label="审核备注" :span="2">
                        {{ currentDetail.verify_remarks || '-' }}
                    </el-descriptions-item>
                </template>
            </el-descriptions>
        </div>

        <template #footer>
            <el-button @click="detailVisible = false">关闭</el-button>
        </template>
    </el-dialog>
</template>

<script lang="ts" setup name="withdrawLists">
import { apiWithdrawLists } from '@/api/finance/withdraw'
import { reactive, ref, onMounted } from 'vue'
import Pagination from '@/components/pagination/index.vue'
import DataPicker from '@/components/data-picker/index.vue'
import WithdrawForm from './components/withdraw-form.vue'
import { usePaging } from '@/hooks/usePaging'

interface FormDataObj {
    bank: string // 提现卡号
    status: string | number // 提现状态
    start_time: string // 开始时间
    end_time: string // 结束时间
}

const formData = reactive<FormDataObj>({
    bank: '',
    status: '',
    start_time: '',
    end_time: ''
})

// 统计数据
const statistics = ref({
    today_amount: '0.00',
    total_amount: '0.00',
    can_withdraw_amount: '0.00',
    has_withdraw_amount: '0.00',
    wait_withdraw_amount: '0.00'
})

// 加载状态
const statisticsLoading = ref(false)

// 详情弹窗相关
const detailVisible = ref(false)
const currentDetail = ref<any>(null)

const { pager, getLists, resetPage, resetParams } = usePaging({
    fetchFun: apiWithdrawLists,
    params: formData
})

// 获取状态标签类型
const getStatusType = (status: number) => {
    const statusMap: Record<number, string> = {
        1: 'warning', // 待审核
        2: 'primary', // 提现中
        3: 'success', // 提现成功
        4: 'danger'   // 提现失败
    }
    return statusMap[status] || 'info'
}

// 格式化金额显示
const formatAmount = (amount: string | number) => {
    if (!amount) return '0.00'
    const num = Number(amount)
    return num.toFixed(2)
}

// 显示详情
const showDetail = (row: any) => {
    currentDetail.value = row
    detailVisible.value = true
}

// 格式化转账时间
const formatTransferTime = (timestamp: number) => {
    if (!timestamp) return '-'
    const date = new Date(timestamp * 1000)
    return date.toLocaleString('zh-CN')
}

// 搜索处理
const handleSearch = () => {
    resetPage()
    getListsWithStats()
}

// 重置处理
const handleReset = () => {
    resetParams()
    getListsWithStats()
}

// 获取列表数据并更新统计信息
const getListsWithStats = async () => {
    try {
        statisticsLoading.value = true
        const res = await getLists()
        if (res?.extend) {
            statistics.value = {
                today_amount: res.extend.today_amount || '0.00',
                total_amount: res.extend.total_amount || '0.00',
                can_withdraw_amount: res.extend.can_withdraw_amount || '0.00',
                has_withdraw_amount: res.extend.has_withdraw_amount || '0.00',
                wait_withdraw_amount: res.extend.wait_withdraw_amount || '0.00'
            }
        }
    } catch (error) {
        console.error('获取数据失败:', error)
    } finally {
        statisticsLoading.value = false
    }
}

onMounted(() => {
    getListsWithStats()
})
</script>

<style scoped>
.grid {
    display: grid;
}
.grid-cols-5 {
    grid-template-columns: repeat(5, minmax(0, 1fr));
}
.gap-4 {
    gap: 1rem;
}
.text-center {
    text-align: center;
}
.text-2xl {
    font-size: 1.5rem;
    line-height: 2rem;
}
.font-bold {
    font-weight: 700;
}
.text-blue-600 {
    color: #2563eb;
}
.text-green-600 {
    color: #16a34a;
}
.text-orange-600 {
    color: #ea580c;
}
.text-purple-600 {
    color: #9333ea;
}
.text-red-600 {
    color: #dc2626;
}
.text-gray-500 {
    color: #6b7280;
}
.mt-1 {
    margin-top: 0.25rem;
}
.font-medium {
    font-weight: 500;
}
.text-lg {
    font-size: 1.125rem;
    line-height: 1.75rem;
}

/* 响应式布局 */
@media (max-width: 1200px) {
    .grid-cols-5 {
        grid-template-columns: repeat(3, minmax(0, 1fr));
    }
}

@media (max-width: 768px) {
    .grid-cols-5 {
        grid-template-columns: repeat(2, minmax(0, 1fr));
    }
    .text-2xl {
        font-size: 1.25rem;
        line-height: 1.75rem;
    }
}

@media (max-width: 480px) {
    .grid-cols-5 {
        grid-template-columns: repeat(1, minmax(0, 1fr));
    }
}

/* 表格样式优化 */
:deep(.el-table__row:hover > td) {
    background-color: #f5f7fa;
}


</style>
