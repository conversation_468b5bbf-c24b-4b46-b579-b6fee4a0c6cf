<template>
    <div>
        <el-card shadow="never" class="!border-none">
            <el-form :model="formData" inline>
                <el-form-item label="返佣时间">
                    <DataPicker
                        v-model:start_time="formData.start_time"
                        v-model:end_time="formData.end_time"
                    />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" @click="resetPage">查询</el-button>
                    <el-button @click="resetParams">重置</el-button>
                </el-form-item>
            </el-form>
        </el-card>

        <el-card shadow="never" class="mt-4 !border-none">
            <div class="mt-4">
                <el-table
                    ref="tableData"
                    size="large"
                    :data="pager.lists"
                    v-loading="pager.loading"
                >
                    <el-table-column property="order_sn" label="订单编号" min-width="180">
                        <template #default="scope">
                            <div>{{ scope.row.order_sn }}</div>
                        </template>
                    </el-table-column>
                    <el-table-column property="title" label="标题" min-width="200">
                        <template #default="scope">
                            <div>{{ scope.row.title || '-' }}</div>
                        </template>
                    </el-table-column>
                    <el-table-column label="飞手信息" min-width="120">
                        <template #default="scope">
                            <el-popover
                                placement="top-start"
                                width="200px"
                                trigger="hover"
                                v-if="scope.row.orders?.staff"
                            >
                                <div class="flex">
                                    <span class="flex-none mr-2">姓名：</span>
                                    <span>{{ scope.row.orders.staff?.name }}</span>
                                </div>
                                <div class="flex mt-2">
                                    <span class="flex-none mr-2">手机号：</span>
                                    <span>{{ scope.row.orders.staff?.mobile }}</span>
                                </div>
                                <template #reference>
                                    <div class="pointer normal">
                                        {{ scope.row.orders.staff?.name }}
                                    </div>
                                </template>
                            </el-popover>
                            <div v-else>-</div>
                        </template>
                    </el-table-column>
                    <el-table-column label="用户信息" min-width="120">
                        <template #default="scope">
                            <el-popover placement="top-start" width="200px" trigger="hover" v-if="scope.row.orders?.user">
                                <div class="flex">
                                    <span class="flex-none mr-2">昵称：</span>
                                    <span>{{ scope.row.orders.user?.nickname }}</span>
                                </div>
                                <div class="flex mt-2">
                                    <span class="flex-none mr-2">手机号：</span>
                                    <span>{{ scope.row.orders.user?.mobile }}</span>
                                </div>
                                <template #reference>
                                    <div class="pointer normal">
                                        {{ scope.row.orders.user?.nickname }}
                                    </div>
                                </template>
                            </el-popover>
                            <div v-else>-</div>
                        </template>
                    </el-table-column>
                    <el-table-column property="service_fee" label="预约服务费" min-width="120">
                        <template #default="scope">
                            <div>{{ scope.row.orders.total_amount || '0.00' }}</div>
                        </template>
                    </el-table-column>
                    <el-table-column property="second_payment" label="二次付款金额" min-width="130">
                        <template #default="scope">
                            <div>{{ scope.row.orders.difference_price || '0.00' }}</div>
                        </template>
                    </el-table-column>
                    <el-table-column property="commission_amount" label="佣金金额" min-width="120">
                        <template #default="scope">
                            <div class="text-green-600 font-medium">¥{{ scope.row.commission_amount || '0.00' }}</div>
                        </template>
                    </el-table-column>
                    <el-table-column property="commission_time" label="返佣时间" min-width="160">
                        <template #default="scope">
                            <div>{{ scope.row.rebate_time || '-' }}</div>
                        </template>
                    </el-table-column>
                </el-table>
            </div>
            <div class="flex justify-end mt-4">
                <Pagination
                    v-model="pager"
                    @change="getLists"
                />
            </div>
        </el-card>
    </div>
</template>

<script lang="ts" setup name="commissionLists">
import { apiCommissionLists } from '@/api/finance/commission'
import { reactive } from 'vue'
import Pagination from '@/components/pagination/index.vue'
import DataPicker from '@/components/data-picker/index.vue'
import { usePaging } from '@/hooks/usePaging'

interface formDataObj {
    order_sn: string // 订单编号
    user_info: string // 用户信息
    staff_info: string // 飞手信息
    status: string | number // 佣金状态
    start_time: string // 开始时间
    end_time: string // 结束时间
}

const formData = reactive<formDataObj>({
    order_sn: '',
    user_info: '',
    staff_info: '',
    status: '',
    start_time: '',
    end_time: ''
})

const { pager, getLists, resetPage, resetParams } = usePaging({
    fetchFun: apiCommissionLists,
    params: formData
})

getLists()
</script>

<style lang="scss" scoped>
.ls-input {
    width: 280px;
}

.pointer {
    cursor: pointer;
}

.normal {
    color: #333;
}

.normal:hover {
    color: #4a5dff;
    text-decoration: underline;
}
</style>
