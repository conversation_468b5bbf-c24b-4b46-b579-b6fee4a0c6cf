/**
 * 默认菜单配置
 * 当没有从后端获取到动态菜单时使用
 */

export interface MenuConfig {
    paths: string
    name: string
    type: 'M' | 'C' | 'A' // M=目录, C=菜单, A=按钮
    is_show: number
    is_cache: number
    component?: string
    icon?: string
    perms?: string
    params?: string // 路由参数，JSON字符串格式
    children?: MenuConfig[]
}

// 默认菜单配置
export const defaultMenuConfig: MenuConfig[] = [
    {
        paths: 'workbench',
        name: '工作台',
        type: 'C',
        is_show: 1,
        is_cache: 0,
        component: 'workbench/index',
        icon: 'el-icon-Monitor',
        perms: 'workbench'
    },
    {
        paths: 'user',
        name: '用户管理',
        type: 'M',
        is_show: 1,
        is_cache: 0,
        icon: 'el-icon-User',
        children: [
            {
                paths: 'userList',
                name: '用户列表',
                type: 'C',
                is_show: 1,
                is_cache: 0,
                component: 'user/userList/index',
                perms: 'user.user/lists'
            }
        ]
    },
    {
        paths: 'master_worker',
        name: '飞手管理',
        type: 'M',
        is_show: 1,
        is_cache: 0,
        icon: 'el-icon-UserFilled',
        children: [
            {
                paths: 'lists',
                name: '飞手列表',
                type: 'C',
                is_show: 1,
                is_cache: 0,
                component: 'master_worker/lists/index',
                perms: 'staff.staff/lists'
            },
        ]
    },
    {
        paths: 'order',
        name: '订单管理',
        type: 'M',
        is_show: 1,
        is_cache: 0,
        icon: 'el-icon-List',
        children: [
            {
                paths: 'lists',
                name: '订单列表',
                type: 'C',
                is_show: 1,
                is_cache: 0,
                component: 'order/lists/index',
                perms: 'order.order/lists'
            },
            {
                paths: 'comment',
                name: '服务评价',
                type: 'C',
                is_show: 1,
                is_cache: 0,
                component: 'service/evaluate/index',
                perms: 'service.evaluate/lists'
            },
        ]
    },
    {
        paths: 'assets',
        name: '我的资产',
        type: 'M',
        is_show: 1,
        is_cache: 0,
        icon: 'el-icon-wallet',
        children: [
            {
                paths: 'withdraw_record',
                name: '提现记录',
                type: 'C',
                is_show: 1,
                is_cache: 0,
                component: 'finance/withdraw/index',
                perms: 'finance.withdraw/lists'
            },
            {
                paths: 'commission_record',
                name: '佣金记录',
                type: 'C',
                is_show: 1,
                is_cache: 0,
                component: 'finance/commission/index',
                perms: 'partner.commission/lists'
            },
        ]
    },
    {
        paths: 'system',
        name: '系统管理',
        type: 'M',
        is_show: 1,
        is_cache: 0,
        icon: 'el-icon-setting',
        children: [
            {
                paths: 'setting',
                name: '修改密码',
                type: 'C',
                is_show: 1,
                is_cache: 0,
                component: 'user/setting',
                perms: 'user.setting/password'
            },
        ]
    }
]

/**
 * 获取默认菜单数据
 */
export function getDefaultMenuData(): MenuConfig[] {
    return defaultMenuConfig
}
