<template>
    <!-- Header Start -->
    <el-card class="!border-none" shadow="never">
        <el-page-header content="用户详情" @back="$router.back()" />
    </el-card>
    <!-- Header End -->

    <!-- Main Start -->
    <el-card shadow="never" class="mt-4 card !border-none" :body-style="{ padding: '20px 0px' }">
        <template #header>
            <span class="text-lg font-semibold">基本资料</span>
        </template>

        <div class="top-container">
            <div class="avatar">
                <div class="mb-2 ml-3">用户头像</div>
                <div class="avatar-container">
                    <el-image style="width: 80px; height: 80px; border-radius: 50%" :src="userInfo.avatar"
                        :preview-src-list="[userInfo.avatar]" :fit="'cover'" />
                    <div class="avatar-edit">
                        <material-select v-model="avatarUrl" type="image" size="80px" @change="handleAvatarChange">
                            <template #upload>
                                <div class="upload-avatar-btn">
                                    <el-icon><edit-pen /></el-icon>
                                    <span>修改头像</span>
                                </div>
                            </template>
                        </material-select>
                    </div>
                </div>
            </div>

            <div class="account">
                <div class="my-4">可用余额</div>
                <div class="price-text">
                    ￥{{ userInfo.user_money }}
                    <!-- <el-button link type="primary" @click="handleadjust()">调整</el-button> -->
                </div>
            </div>
        </div>

        <el-form label-position="right" label-width="auto" :model="userInfo" class="mt-6 px-2">
            <el-form-item label="用户账号:">
                {{ userInfo.account || '-' }}
            </el-form-item>
            <el-form-item label="用户昵称:">
                <div class="flex">
                    {{ userInfo.nickname || '-' }}
                    <popover-input @confirm="handleUserInfoEdit($event, 'nickname')" v-model="userInfo.nickname"
                        type="text">
                        <el-icon style="vertical-align: middle" color="#4A5DFF" class="ml-2">
                            <edit-pen />
                        </el-icon>
                    </popover-input>
                </div>
            </el-form-item>
            <el-form-item label="真实姓名:">
                <div class="flex">
                    {{ userInfo.real_name || '-' }}
                    <popover-input @confirm="handleUserInfoEdit($event, 'real_name')" v-model="userInfo.real_name"
                        type="text">
                        <el-icon style="vertical-align: middle" color="#4A5DFF" class="ml-2">
                            <edit-pen />
                        </el-icon>
                    </popover-input>
                </div>
            </el-form-item>
            <el-form-item label="性别:">
                <div class="flex">
                    {{ userInfo.sex_desc || '-' }}
                    <popover-user @confirm="handleUserInfoEdit($event, 'sex')" changeType="sex" v-model="userInfo.sex">
                        <el-icon style="vertical-align: middle" color="#4A5DFF" class="ml-2">
                            <edit-pen />
                        </el-icon>
                    </popover-user>
                </div>
            </el-form-item>
            <el-form-item label="联系电话:">
                <div class="flex">
                    {{ userInfo.mobile || '-' }}
                    <popover-input @confirm="handleUserInfoEdit($event, 'mobile')" v-model="userInfo.mobile"
                        type="text">
                        <el-icon style="vertical-align: middle" color="#4A5DFF" class="ml-2">
                            <edit-pen />
                        </el-icon>
                    </popover-input>
                </div>
            </el-form-item>

            <el-form-item label="所属地区:">
                <div class="flex">
                    {{ userInfo.province }} {{ userInfo.city }} {{ userInfo.area || '-' }}
                    <popover-area @confirm="handleAreaChange" v-model="areaInfo">
                        <el-icon style="vertical-align: middle" color="#4A5DFF" class="ml-2">
                            <edit-pen />
                        </el-icon>
                    </popover-area>
                </div>
            </el-form-item>
            <!-- <el-form-item label="注册来源:">
                {{ userInfo.source_desc || '-' }}
            </el-form-item>-->
            <el-form-item label="注册时间:">
                {{ userInfo.create_time || '-' }}
            </el-form-item>
            <el-form-item label="最近登录时间:">
                {{ userInfo.login_time || '-' }}
            </el-form-item>
        </el-form>
    </el-card>

    <edit-popup v-if="showEdit" ref="editRef" @close="showEdit = false" :id="route.query.id"
        :value="userInfo.user_money" :type="1" @success="initUserInfoFunc"></edit-popup>
</template>

<script lang="ts" setup>
import { ref, nextTick, shallowRef } from 'vue'
import { apiUserDetail, apiSetUserInfo } from '@/api/user'
import PopoverInput from '@/components/popover-input/index.vue'
import PopoverUser from './components/popover-user.vue'
import PopoverArea from './components/popover-area.vue'
import EditPopup from './components/adjust.vue'
import MaterialSelect from '@/components/material-select/index.vue'
import { useRoute } from 'vue-router'

/** Interface Start **/
interface UserInfoObj {
    id: number | string //	int	id
    sn: string //	string	用户编码
    nickname: string //	string	用户昵称
    avatar: string //	string	用户头像
    mobile: number | string //	string	手机号码
    sex: number //	int	用户性别
    sex_desc: string //	string	性别描述
    real_name: string //	string	真实姓名
    login_time: string //	string	最近登录时间
    register_source: number //	int	注册来源
    source_desc: string //	string	来源描述
    create_time: string //	string	注册时间
    account?: string
    user_money: string
    province_id?: number | string
    province?: string
    city_id?: number | string
    city?: string
    area_id?: number | string
    area?: string
}
/** Interface End **/

/** Data Start **/
const showEdit = ref(false)
const editRef = shallowRef<InstanceType<typeof EditPopup>>()
const route = useRoute()
const avatarUrl = ref('')
const userInfo = ref<UserInfoObj>({
    id: '',
    sn: '',
    nickname: '',
    avatar: '',
    mobile: '',
    sex: 0,
    sex_desc: '',
    real_name: '',
    login_time: '',
    register_source: 0,
    source_desc: '',
    create_time: '',
    user_money: '',
    province_id: "",
    province: "",
    city_id: "",
    city: "",
    area_id: "",
    area: ""
})

// 地区信息对象
const areaInfo = ref({
    province: '',
    province_id: '',
    city: '',
    city_id: '',
    area: '',
    area_id: ''
})
/** Data End **/

/** Methods Start **/
// 初始化用户数据
const initUserInfoFunc = async (): Promise<void> => {
    const res: any = await apiUserDetail({ id: route.query.id })
    userInfo.value = res

    // 同步更新地区信息
    areaInfo.value = {
        province: res.province || '',
        province_id: res.province_id || '',
        city: res.city || '',
        city_id: res.city_id || '',
        area: res.area || '',
        area_id: res.area_id || ''
    }
}

/**
 * @description 编辑用户信息
 */
const handleUserInfoEdit = async (event: string, type: string): Promise<void> => {
    await apiSetUserInfo({
        id: userInfo.value.id,
        field: type,
        value: event
    })
    initUserInfoFunc()
}

/**
 * @description 处理头像修改
 */
const handleAvatarChange = async (url: string): Promise<void> => {
    if (url) {
        await handleUserInfoEdit(url, 'avatar')
        avatarUrl.value = url
    }
}

/**
 * @description 处理地区修改
 */
const handleAreaChange = async (areaData: any): Promise<void> => {
    if (areaData) {
        // 创建包含省市区ID的JSON对象
        const addressObj = {
            province_id: areaData.province_id,
            city_id: areaData.city_id,
            area_id: areaData.area_id,
        };
        
        // 将对象转换为JSON字符串
        const addressJson = JSON.stringify(addressObj);
        
        // 保存为address字段
        await handleUserInfoEdit(addressJson, 'address');
    }
}

const handleadjust = async () => {
    showEdit.value = true
    await nextTick()
    editRef.value?.open()
}
/** Methods End **/

/** Life Cycle Start **/
initUserInfoFunc()
/** Life Cycle End **/
</script>

<style lang="scss">
.card {
    padding: 0 20px;

    .top-container {
        display: flex;
        background-color: #f6f6f6;
        padding: 20px 80px;

        .avatar {
            flex: 0 0 350px;

            .avatar-container {
                position: relative;
                width: 80px;
                height: 80px;
                border-radius: 50%;
                overflow: hidden;

                .avatar-edit {
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 100%;
                    height: 100%;
                    background-color: rgba(0, 0, 0, 0.5);
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    opacity: 0;
                    transition: opacity 0.3s;

                    &:hover {
                        opacity: 1;
                    }

                    .upload-avatar-btn {
                        color: #fff;
                        font-size: 12px;
                        text-align: center;
                        cursor: pointer;

                        .el-icon {
                            font-size: 16px;
                            margin-bottom: 4px;
                        }
                    }

                    :deep(.material-select__trigger) {
                        width: 100%;
                        height: 100%;
                        margin: 0;
                        padding: 0;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                    }

                    :deep(.material-preview),
                    :deep(.material-upload) {
                        margin: 0;
                        width: 100%;
                        height: 100%;
                    }
                }
            }
        }

        .account {
            flex: 0 0 20%;
            text-align: center;
            padding-left: 12px;
        }
    }
}
</style>
